<template>
	<view class="search-container">
		<!-- 搜索栏区域 -->
		<view class="search-section">
			<view class="search-bar" :class="{ active: isSearchActive }">
				<view class="search-input-container">
					<image class="search-icon" :src="isSearchActive ? '/static/search-page-icons/search-red-icon.svg' : '/static/search-page-icons/search-icon.svg'" mode="aspectFit"></image>
					<input
						class="search-input"
						:placeholder="searchPlaceholder"
						v-model="searchText"
						@focus="onSearchFocus"
						@blur="onSearchBlur"
						@input="onSearchInput"
					/>
					<view class="cursor" v-if="isSearchActive && showCursor">|</view>
				</view>
			</view>
			<view class="filter-button" v-if="!isSearchActive">
				<image class="filter-icon" src="/static/search-page-icons/filter-icon.svg" mode="aspectFit"></image>
			</view>
			<view class="cancel-button" v-if="isSearchActive" @click="cancelSearch">
				<image class="cancel-icon" src="/static/search-page-icons/filter-icon.svg" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 搜索历史区域 -->
		<view class="search-history" v-if="isSearchActive && searchHistory.length > 0">
			<view class="history-item" v-for="(item, index) in searchHistory" :key="index" @click="selectHistoryItem(item)">
				<image class="clock-icon" src="/static/search-page-icons/clock-icon.svg" mode="aspectFit"></image>
				<text class="history-text">{{ item }}</text>
			</view>
		</view>

		<!-- 搜索结果区域 -->
		<view class="search-results" v-if="!isSearchActive">
			<text class="no-results">开始搜索测试内容</text>
		</view>

		<!-- 底部导航栏 -->
		<view class="tabbar-container">
			<TabBar currentPage="search" />
		</view>
	</view>
</template>

<script>
import TabBar from '@/components/TabBar.vue'

export default {
	components: {
		TabBar
	},
	data() {
		return {
			isSearchActive: false,
			searchText: '',
			searchPlaceholder: 'Search for a test',
			showCursor: true,
			searchHistory: [
				'Informatics tests',
				'Literary tests'
			]
		}
	},
	onLoad() {
		// 光标闪烁效果
		this.startCursorBlink();
	},
	methods: {
		onSearchFocus() {
			this.isSearchActive = true;
		},
		onSearchBlur() {
			// 延迟处理，避免点击历史项时立即失焦
			setTimeout(() => {
				if (!this.searchText) {
					this.isSearchActive = false;
				}
			}, 200);
		},
		onSearchInput(e) {
			this.searchText = e.detail.value;
		},
		cancelSearch() {
			this.searchText = '';
			this.isSearchActive = false;
		},
		selectHistoryItem(item) {
			this.searchText = item;
			this.isSearchActive = false;
			// 这里可以添加搜索逻辑
			console.log('搜索:', item);
		},
		startCursorBlink() {
			setInterval(() => {
				this.showCursor = !this.showCursor;
			}, 500);
		}
	}
}
</script>

<style scoped>
.search-container {
	width: 100%;
	min-height: 100vh;
	background-color: #FFFFFF;
	display: flex;
	flex-direction: column;
	overflow-x: hidden;
}

/* 搜索栏区域 */
.search-section {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 148rpx 28rpx 32rpx;
	background-color: #FFFFFF;
}

.search-bar {
	flex: 1;
	background-color: #EDEDED;
	border-radius: 100rpx;
	padding: 22rpx 52rpx;
	position: relative;
}

.search-bar.active {
	background-color: #FFFFFF;
	border: 2rpx solid #F2282D;
}

.search-input-container {
	display: flex;
	align-items: center;
	gap: 26rpx;
	position: relative;
}

.search-icon {
	width: 32rpx;
	height: 32rpx;
}

.search-input {
	flex: 1;
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 28rpx;
	font-weight: 500;
	color: #000000;
	border: none;
	outline: none;
	background: transparent;
}

.search-input::placeholder {
	color: #9D9D9D;
}

.cursor {
	position: absolute;
	right: 0;
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 34rpx;
	font-weight: 400;
	color: #F2282D;
	line-height: 1.18;
}

.filter-button {
	width: 110rpx;
	height: 110rpx;
	background-color: #EDEDED;
	border-radius: 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.filter-icon {
	width: 36rpx;
	height: 34rpx;
}

.cancel-button {
	width: 42rpx;
	height: 42rpx;
	background-color: #EDEDED;
	border-radius: 21rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.cancel-icon {
	width: 20rpx;
	height: 17rpx;
}

/* 搜索历史区域 */
.search-history {
	padding: 0 78rpx;
	background-color: #FFFFFF;
}

.history-item {
	display: flex;
	align-items: center;
	gap: 24rpx;
	padding: 34rpx 0;
	border-bottom: 1rpx solid #F0F0F0;
}

.history-item:last-child {
	border-bottom: none;
}

.clock-icon {
	width: 40rpx;
	height: 40rpx;
}

.history-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 28rpx;
	font-weight: 500;
	color: #000000;
	line-height: 1.43;
	letter-spacing: 1.43%;
}

/* 搜索结果区域 */
.search-results {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 100rpx 60rpx;
}

.no-results {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 32rpx;
	font-weight: 400;
	color: #9D9D9D;
	text-align: center;
}

/* TabBar容器 */
.tabbar-container {
	background-color: #FFFFFF;
	padding: 0 34rpx 34rpx;
	display: flex;
	justify-content: center;
}
</style>
