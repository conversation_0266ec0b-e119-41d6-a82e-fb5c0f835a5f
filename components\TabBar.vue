<template>
	<view class="bottom-nav">
		<!-- 悬浮的红色菱形按钮 -->
		<image class="floating-diamond-btn" src="/static/figma-assets/assets/1204c3b8-ba20-4ea6-9dab-a251ea46581f.png" mode="aspectFit" @click="onFloatingButtonClick"></image>

		<view class="nav-item" :class="{ active: currentPage === 'home' }" @click="goToHome">
			<image class="nav-item-icon" :class="{ active: currentPage === 'home' }" src="/static/figma-assets/assets/1f21ef1a-8cf6-4c0a-bc6c-9fb6b8a27882.png" mode="aspectFit"></image>
			<text class="nav-item-text" :class="{ active: currentPage === 'home' }">首页</text>
		</view>
		<view class="nav-item" :class="{ active: currentPage === 'search' }" @click="goToSearch">
			<image class="nav-item-icon" :class="{ active: currentPage === 'search' }" src="/static/figma-assets/assets/380fb7d4-72a0-4354-a8bc-93a37ae37c90.png" mode="aspectFit"></image>
			<text class="nav-item-text" :class="{ active: currentPage === 'search' }">搜索</text>
		</view>
		<view class="nav-item" :class="{ active: currentPage === 'save' }" @click="goToSave">
			<image class="nav-item-icon" :class="{ active: currentPage === 'save' }" src="/static/save-page-icons/bookmark-nav-icon.svg" mode="aspectFit"></image>
			<text class="nav-item-text" :class="{ active: currentPage === 'save' }">保存</text>
		</view>
		<view class="nav-item" :class="{ active: currentPage === 'profile' }" @click="goToProfile">
			<image class="nav-item-icon" :class="{ active: currentPage === 'profile' }" src="/static/figma-assets/assets/09d1a5c7-2c43-47ee-9116-489a7198826d.png" mode="aspectFit"></image>
			<text class="nav-item-text" :class="{ active: currentPage === 'profile' }">我的</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TabBar',
	props: {
		currentPage: {
			type: String,
			default: 'home'
		}
	},
	methods: {
		goToHome() {
			if (this.currentPage !== 'home') {
				uni.reLaunch({
					url: '/pages/index/index'
				});
			}
		},
		goToSearch() {
			if (this.currentPage !== 'search') {
				uni.reLaunch({
					url: '/pages/search/search'
				});
			}
		},
		goToSave() {
			if (this.currentPage !== 'save') {
				uni.reLaunch({
					url: '/pages/save/save'
				});
			}
		},
		goToProfile() {
			if (this.currentPage !== 'profile') {
				uni.reLaunch({
					url: '/pages/profile/profile'
				});
			}
		},
		onFloatingButtonClick() {
			console.log('点击了悬浮菱形按钮');
			// 这里可以添加特殊功能，比如快速操作菜单
		}
	}
}
</script>

<style scoped>
/* 底部导航栏 */
.bottom-nav {
	width: 720rpx;
	height: 228rpx;
	margin: 30rpx auto 0;
	background-color: #F3F3F3;
	border-radius: 48rpx;
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
	padding: 0 48rpx 48rpx;
	box-sizing: border-box;
	position: relative;
}

/* 悬浮的红色菱形按钮 */
.floating-diamond-btn {
	width: 136rpx;
	height: 136rpx;
	position: absolute;
	top: -68rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 10;
}

.nav-item {
	width: 112rpx;
	height: 88rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	border-radius: 4rpx;
}

.nav-item-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 8rpx;
	/* 默认状态：未选中时图标为黑色 */
	filter: brightness(0) saturate(100%);
}

.nav-item-icon.active {
	/* 选中状态：图标变为红色 #F2282D */
	filter: brightness(0) saturate(100%) invert(18%) sepia(95%) saturate(7483%) hue-rotate(355deg) brightness(95%) contrast(95%);
}

.nav-item-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 20rpx;
	font-weight: 500;
	color: #000000;
	text-align: center;
	letter-spacing: 0.20rpx;
	line-height: 40rpx;
}

.nav-item-text.active {
	color: #F2282D;
}
</style>
